export const convertDigitsToPersian = (input: string | number): string => {
  const persianDigits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
  const inputStr = String(input);
  const isNegative = inputStr.startsWith('-');

  let numberStr = isNegative ? inputStr.substring(1) : inputStr;

  const convertedNumber = numberStr.replace(/\d/g, (digit) => persianDigits[parseInt(digit)]);

  return isNegative ? `\u200E-${convertedNumber}` : convertedNumber;
};
